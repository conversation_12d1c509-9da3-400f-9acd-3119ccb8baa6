import axios from 'axios'

const request = axios.create({
  baseURL: '/api',
  timeout: 5000
})

request.interceptors.request.use((config) => {

  return config
})

request.interceptors.response.use(
  (response) => {
    const { code, data, message } = response.data
    
    if (code === 200) {
      return data
    } else {
      throw new Error(message || '请求失败')
    }
  },
  (error) => {
    throw new Error(error.message)
  }
)

export default request