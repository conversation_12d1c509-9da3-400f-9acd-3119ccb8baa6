<script setup lang="ts">
import Banner from './banner/index.vue'
import Search from './search/index.vue'
import Option from './option/index.vue'
import Card from './card/index.vue'

const levels = ['三级甲等', '三级乙等', '二级甲等', '二级乙等', '一级甲等', '一级乙等']
const regions = ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区']


import { onMounted } from 'vue'
import { ElMessage } from 'element-plus'

onMounted(async () => {
  try {
    const res = await request.get('/hosp/hospital/1/10')
    console.log("医院列表", res)
  } catch (error) {
    ElMessage.error('获取医院列表失败')
  }
})

import logo from '@/assets/images/logo.png'
const hospitals = [
  {name: '北京市顺义区医院', level: '三级甲等', time: '09:15', logo},
  {name: '北京市隆福医院', level: '二级乙等', time: '08:00', logo},
  {name: '首都医科大学附属北京同仁医院', level: '三级乙等', time: '09:30', logo},
  {name: '首都医科大学附属北京儿童医院', level: '三级甲等', time: '07:10', logo},
  {name: '北京中医药大学房山医院', level: '二级乙等', time: '12:10', logo},
  {name: '首都医科大学宣武医院', level: '三级乙等', time: '18:00', logo},
  {name: '首都医科大学附属北京安贞医院', level: '三级甲等', time: '20:00', logo},
  {name: '首都医科大学附属北京天坛医院', level: '二级甲等', time: '13:00', logo},
]

import {ref} from 'vue'
let currentPage = ref<number>(1)
let pageSize = ref<number>(10)

</script>

<template>
  <Banner />
  <Search />
  <el-row :gutter="20">
    <el-col :span="20" class="option">
      <span>医院</span>
      <Option name="等级" :options="levels" />
      <Option name="地区" :options="regions" />
      <div class="hospital">
        <Card class="card" v-for="hospital in hospitals" key="hospital.name"
          :name="hospital.name"
          :level="hospital.level"
          :time="hospital.time"
          :logo="hospital.logo"
        />
        <el-pagination class="page"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 40]"
          layout="prev, pager, next, ->, sizes, total"
          :total="8"
        />
      </div>
    </el-col>
    <el-col :span="4">
      456
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.option {
  span {
    color: #7f7f7f;
    display: inline-block;
    margin: 10px 0;
  }
}

.hospital {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
  .card {
    width: 48%;
  }
  .page {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
